#![no_std]

//! # ERU WiFi - Reusable WiFi Manager for ESP32 Embedded Rust
//!
//! A clean, ergonomic WiFi management library for ESP32 microcontrollers that eliminates
//! boilerplate code duplication across embedded Rust projects.
//!
//! ## Features
//!
//! - `no_std` compatible for embedded environments
//! - Static lifetimes throughout to avoid lifetime complexity
//! - Clean, ergonomic API for WiFi operations
//! - Built on the mature `esp-wifi` ecosystem
//! - Thread-safe state management
//! - Comprehensive error handling
//!
//! ## Example Usage
//!
//! ```rust,no_run
//! use eru_wifi::{WifiManager, WifiCredentials, WifiConfig};
//!
//! // Initialize esp-wifi components (existing boilerplate)
//! let (controller, device) = esp_wifi::wifi::new_with_mode(&init, WifiMode::Sta)?;
//!
//! // Create credentials
//! let credentials = WifiCredentials::new("MyNetwork", "password123")?;
//! let config = WifiConfig::new(credentials);
//!
//! // Create WifiManager with clean API
//! let mut wifi = WifiManager::new(controller, device, config);
//!
//! // Simple connection
//! wifi.connect()?;
//!
//! // Check status
//! if wifi.is_connected() {
//!     println!("Connected successfully!");
//! }
//! ```

use esp_wifi::wifi::{AuthMethod, WifiController, WifiDevice, WifiError, WifiState};
use heapless::String;

mod config;
mod error;
mod manager;
mod state;

pub use config::{WifiConfig, WifiCredentials};
pub use error::WifiManagerError;
pub use manager::WifiManager;
pub use state::ConnectionState;
